# Notification System

This document describes the scheduled notification system implemented for match reminders.

## Overview

The app now sends local notifications to users at 9:30 AM and 7:30 PM daily with the message:
"The next match starts in 30 minutes. Make sure you pick your best team!"

These notifications are only sent when:
1. The user has enabled "Pre-Match notifications" with push notifications enabled
2. The app is not currently running in the foreground

## Implementation

### Files Added/Modified

1. **`src/services/NotificationScheduler.ts`** - Core notification scheduling service
2. **`src/hooks/useNotificationScheduler.ts`** - React hook for managing notifications
3. **`src/components/NotificationTestButton.tsx`** - Development testing component
4. **`app.json`** - Added expo-notifications plugin configuration
5. **`app/_layout.tsx`** - Added notification initialization
6. **`src/screens/NotificationSettingsScreen.tsx`** - Updated to handle scheduling
7. **`src/screens/HomeScreen.tsx`** - Added test buttons for development

### Key Features

- **Daily Scheduling**: Notifications are scheduled to repeat daily at 9:30 AM and 7:30 PM
- **Permission Handling**: Checks for notification permissions before scheduling
- **Preference Integration**: Respects user's notification preferences
- **Automatic Management**: Schedules/cancels notifications when preferences change
- **Development Testing**: Test buttons available in development mode

### Configuration

The notification system is configured in `app.json`:

```json
[
  "expo-notifications",
  {
    "icon": "./assets/icon.png",
    "color": "#ffffff",
    "defaultChannel": "default"
  }
]
```

### Usage

The notification system works automatically:

1. When the app starts, it checks the user's pre-match notification preferences
2. If push notifications are enabled for pre-match notifications, it schedules the daily reminders
3. When users change their notification preferences, the system updates the scheduled notifications accordingly

### Testing

In development mode, test buttons are available on the home screen:

- **Test Notification**: Schedules a test notification that fires in 10 seconds
- **Show Scheduled**: Logs all currently scheduled notifications to the console

### Technical Details

- Uses `expo-notifications` for local notification scheduling
- Stores notification IDs in AsyncStorage for management
- Handles permission requests automatically
- Integrates with existing notification preference system
- Only shows notifications when the app is not in the foreground

### Storage Keys

The system uses these AsyncStorage keys:

- `@morning_notification_id` - ID of the morning notification
- `@evening_notification_id` - ID of the evening notification
- `@notification_preMatch_push` - User's pre-match push notification preference

### Error Handling

The system includes comprehensive error handling:

- Gracefully handles permission denials
- Logs errors for debugging
- Continues to function even if some operations fail
- Provides fallback behavior for edge cases

## Future Enhancements

Potential improvements could include:

1. Customizable notification times
2. Different messages for different times
3. Integration with actual match schedules
4. Notification history tracking
5. More granular notification preferences
