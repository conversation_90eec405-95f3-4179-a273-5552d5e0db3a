import { Authenticator } from '@aws-amplify/ui-react-native';
import { Nunito_400Regular, Nunito_700Bold, useFonts } from '@expo-google-fonts/nunito';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Amplify } from 'aws-amplify';
import { Slot } from 'expo-router';
import React, { useEffect } from 'react';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import styled from 'styled-components/native';
import awsExports from '../src/aws-exports';
import { AuthProvider } from '../src/context/AuthContext';
import { ManagerProvider } from '../src/context/ManagerContext';
import { useNotificationScheduler } from '../src/hooks/useNotificationScheduler';
import { ThemeProvider } from '../src/theme/ThemeContext';
import Callback from './callback';

Amplify.configure(awsExports);

const StyledGestureHandlerRootView = styled(GestureHandlerRootView)`
  flex: 1;
`;

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 1000 * 60 * 5, // Data is considered fresh for 5 minutes
      gcTime: 1000 * 60 * 30, // Cache is kept for 30 minutes
    },
  },
});

function NotificationInitializer() {
  const { updateNotificationScheduling } = useNotificationScheduler();

  useEffect(() => {
    // Initialize notification scheduling when the app starts
    updateNotificationScheduling();
  }, []);

  return null;
}

export default function Root() {
  const [fontsLoaded] = useFonts({
    Nunito: Nunito_400Regular,
    NunitoBold: Nunito_700Bold,
  });

  return (
    <StyledGestureHandlerRootView>
      <ThemeProvider>
        <QueryClientProvider client={queryClient}>
          <Authenticator.Provider>
            <AuthProvider>
              <Callback />
              <ManagerProvider>
                <NotificationInitializer />
                <Slot />
              </ManagerProvider>
            </AuthProvider>
          </Authenticator.Provider>
        </QueryClientProvider>
      </ThemeProvider>
    </StyledGestureHandlerRootView>
  );
}
