import { MaterialIcons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import React from 'react';
import { Platform } from 'react-native';
import styled, { DefaultTheme } from 'styled-components/native';
import { CrossPlatformAlert } from '../components/CrossPlatformAlert';
import { NotificationTestButton } from '../components/NotificationTestButton';
import { Text } from '../components/Text';
import { useManager } from '../context/ManagerContext';
import { useTheme } from '../theme/ThemeContext';
import { formatPlayerValue } from '../utils/PlayerUtils';

interface StyledProps {
  theme: DefaultTheme;
}

const Container = styled.ScrollView`
  flex: 1;
  background-color: ${(props: StyledProps) => props.theme.colors.background};
`;

const Header = styled.View`
  padding: 20px;
  padding-top: 40px;
  background-color: ${(props: StyledProps) => props.theme.colors.surface};
  border-bottom-left-radius: 20px;
  border-bottom-right-radius: 20px;
  margin-bottom: 10px;
  ${Platform.select({
    ios: `
      shadow-color: ${(props: StyledProps) => props.theme.colors.shadow};
      shadow-offset: 0px 2px;
      shadow-opacity: 0.25;
      shadow-radius: 3.84px;
    `,
    android: `
      elevation: 3;
    `,
  })}
`;

const TeamName = styled(Text)`
  font-size: 24px;
  font-weight: bold;
  color: ${(props: StyledProps) => props.theme.colors.text.primary};
  text-align: center;
  margin-bottom: 5px;
`;

const TeamBalance = styled(Text)`
  font-size: 18px;
  color: ${(props: StyledProps) => props.theme.colors.text.secondary};
  text-align: center;
`;

const ContentContainer = styled.View`
  padding: 20px;
`;

const IconGrid = styled.View`
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: space-between;
  padding: 20px;
`;

const IconContainer = styled.TouchableOpacity`
  width: 45%;
  aspect-ratio: 1;
  background-color: ${(props: StyledProps) => props.theme.colors.surface};
  border-radius: 10px;
  margin: 10px 0;
  justify-content: center;
  align-items: center;
  ${Platform.select({
    ios: `
      shadow-color: ${(props: StyledProps) => props.theme.colors.shadow};
      shadow-offset: 0px 2px;
      shadow-opacity: 0.25;
      shadow-radius: 3.84px;
    `,
    android: `
      elevation: 3;
    `,
  })}
`;

const IconText = styled(Text)`
  margin-top: 10px;
  font-size: 16px;
  font-weight: bold;
  color: ${(props: StyledProps) => props.theme.colors.text.primary};
`;

const ThemeButton = styled.TouchableOpacity`
  position: absolute;
  top: 20px;
  right: 20px;
  padding: 10px;
  z-index: 1;
  background-color: ${(props: StyledProps) => props.theme.colors.surface};
  border-radius: 20px;
  ${Platform.select({
    ios: `
      shadow-color: ${(props: StyledProps) => props.theme.colors.shadow};
      shadow-offset: 0px 2px;
      shadow-opacity: 0.25;
      shadow-radius: 3.84px;
    `,
    android: `
      elevation: 3;
    `,
  })}
`;

type MenuItem = {
  name: string;
  icon: keyof typeof MaterialIcons.glyphMap;
  href: string;
};

const HomeScreen = () => {
  const router = useRouter();
  const { isDark, themePreference, setThemePreference, theme } = useTheme();
  const { team, loading } = useManager();
  const [isAlertVisible, setAlertVisible] = React.useState(false);

  // Get the team balance
  const teamBalance = team?.balance || 0;

  const handleThemePress = () => {
    setAlertVisible(true);
  };

  const menuItems: MenuItem[] = [
    { name: 'Team', icon: 'group', href: '/team' },
    { name: 'League', icon: 'emoji-events', href: '/league' },
    { name: 'Fixtures', icon: 'event', href: '/fixtures' },
    { name: 'Transfers', icon: 'swap-horiz', href: '/transfers' },
    { name: 'Profile', icon: 'person', href: '/manager-profile' },
    { name: 'Notifications', icon: 'notifications-active', href: '/notification-settings' },
  ];

  return (
    <Container>
      <ThemeButton onPress={handleThemePress}>
        <MaterialIcons
          name={
            themePreference === 'system'
              ? 'settings-brightness'
              : isDark
                ? 'light-mode'
                : 'dark-mode'
          }
          size={24}
          color={theme.colors.text.primary}
        />
      </ThemeButton>

      <Header>
        <TeamName>{loading ? 'Loading...' : team?.teamName || 'Your Team'}</TeamName>
        <TeamBalance>Balance: {formatPlayerValue(teamBalance)}</TeamBalance>
      </Header>

      <ContentContainer>
        <IconGrid>
          {menuItems.map((item) => (
            <IconContainer key={item.name} onPress={() => router.push(item.href)}>
              <MaterialIcons name={item.icon} size={40} color={theme.colors.text.primary} />
              <IconText>{item.name}</IconText>
            </IconContainer>
          ))}
        </IconGrid>
      </ContentContainer>

      <CrossPlatformAlert
        visible={isAlertVisible}
        title="Theme Settings"
        message="Choose your preferred theme"
        buttons={[
          {
            text: 'System',
            onPress: () => setThemePreference('system'),
            style: themePreference === 'system' ? 'destructive' : 'default',
          },
          {
            text: 'Light',
            onPress: () => setThemePreference('light'),
            style: themePreference === 'light' ? 'destructive' : 'default',
          },
          {
            text: 'Dark',
            onPress: () => setThemePreference('dark'),
            style: themePreference === 'dark' ? 'destructive' : 'default',
          },
          {
            text: 'Cancel',
            style: 'cancel',
          },
        ]}
        onDismiss={() => setAlertVisible(false)}
      />
    </Container>
  );
};

export default HomeScreen;
