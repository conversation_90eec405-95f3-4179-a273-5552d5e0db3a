import { FontAwesome, MaterialIcons } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as Notifications from 'expo-notifications';
import { router } from 'expo-router';
import React, { useEffect, useState } from 'react';
import { ActivityIndicator, Platform, Switch, View } from 'react-native';
import styled from 'styled-components/native';
import { callApi } from '../api/client';
import { Container, Description, StyledProps, Title } from '../components/Common';
import { CrossPlatformAlert } from '../components/CrossPlatformAlert';
import { Text } from '../components/Text';
import { NOTIFICATION_SETTINGS_SEEN_KEY, useManager } from '../context/ManagerContext';
import { useNotificationScheduler } from '../hooks/useNotificationScheduler';
import { useTheme } from '../theme/ThemeContext';

const SettingContainer = styled.View`
  padding: 15px 0;
  border-bottom-width: 1px;
  border-bottom-color: ${(props: StyledProps) => props.theme.colors.border};
`;

const SettingHeader = styled.View`
  flex-direction: row;
  align-items: center;
  margin-bottom: 10px;
  gap: 10px;
`;

const SettingContent = styled.View`
  flex-direction: ${Platform.OS === 'web' ? 'row' : 'column'};
  justify-content: space-between;
  align-items: ${Platform.OS === 'web' ? 'center' : 'flex-start'};
`;

const SettingLabel = styled(Text)`
  font-size: 16px;
  flex: 1;
`;

const SettingDescription = styled(Text)`
  font-size: 14px;
  color: ${(props: StyledProps) => props.theme.colors.text.secondary};
  flex: 1;
  margin-right: ${Platform.OS === 'web' ? '20px' : '0'};
  margin-bottom: ${Platform.OS === 'web' ? '0' : '10px'};
`;

const ToggleRow = styled.View`
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  min-width: 200px;
`;

const ToggleColumn = styled.View`
  flex-direction: row;
  align-items: center;
  width: 100px;
  justify-content: center;
`;

const ColumnHeaderText = styled(Text)`
  font-size: 14px;
  font-family: ${(props: StyledProps) => props.theme.typography.bold};
  text-align: center;
  width: 100px;
`;

const IconContainer = styled.View`
  margin-right: 0px;
`;

const ButtonContainer = styled.View`
  margin-top: 40px;
`;

const DebugContainer = styled.View`
  margin-top: 40px;
  padding-top: 20px;
  border-top-width: 1px;
  border-top-color: ${(props: StyledProps) => props.theme.colors.border};
`;

const DebugButton = styled.TouchableOpacity`
  background-color: ${(props: StyledProps) => props.theme.colors.error};
  padding: 10px;
  border-radius: 8px;
  align-items: center;
  margin-top: 10px;
`;

const SaveButton = styled.TouchableOpacity`
  background-color: ${(props: StyledProps) => props.theme.colors.primary};
  padding: 15px;
  border-radius: 8px;
  align-items: center;
`;

const ButtonText = styled(Text)`
  color: white;
  font-size: 16px;
  font-family: ${(props: StyledProps) => props.theme.typography.bold};
`;

// Define notification types and their descriptions
interface NotificationType {
  key: string;
  title: string;
  description: string;
  icon: string;
}

// API interface for notification preferences
interface NotificationTypePreference {
  push: boolean;
  email: boolean;
}

interface NotificationPreferencesRequestBody {
  transfers: NotificationTypePreference;
  preMatch: NotificationTypePreference;
  scoutingResults: NotificationTypePreference;
  postMatch: NotificationTypePreference;
  announcements: NotificationTypePreference;
  pushNotificationToken?: string;
}

const notificationTypes: NotificationType[] = [
  {
    key: 'preMatch',
    title: 'Pre-Match notifications',
    description: 'Reminders to pick your team before matches start',
    icon: 'futbol-o',
  },
  {
    key: 'postMatch',
    title: 'Post-Match notifications',
    description: 'Updates about match results and performance',
    icon: 'trophy',
  },
  {
    key: 'transfers',
    title: 'Transfer notifications',
    description:
      "We'll let you know if someone wants to buy one of your players and update you on your transfer bids",
    icon: 'exchange',
  },
  {
    key: 'scoutingResults',
    title: 'Scouting notifications',
    description: "We'll message you when the scout returns with new player recommendations",
    icon: 'binoculars',
  },
  {
    key: 'announcements',
    title: 'Announcements',
    description: 'Important updates and announcements about the game',
    icon: 'bullhorn',
  },
];

const NotificationSettingsScreen = () => {
  const { manager } = useManager();
  const { theme } = useTheme();
  const { updateNotificationScheduling } = useNotificationScheduler();
  const [isSaving, setIsSaving] = useState(false);
  const [showConfirmation, setShowConfirmation] = useState(false);

  // State for notification preferences
  const [preferences, setPreferences] = useState<{
    [key: string]: { email: boolean; push: boolean };
  }>({
    preMatch: { email: true, push: true },
    postMatch: { email: true, push: true },
    transfers: { email: true, push: true },
    scoutingResults: { email: true, push: true },
    announcements: { email: true, push: true },
  });

  // Load saved preferences if they exist
  useEffect(() => {
    const loadPreferences = async () => {
      try {
        // First try to load preferences from the manager object
        if (manager?.notificationPreferences) {
          const managerPreferences = manager.notificationPreferences;

          // Update state with manager preferences
          setPreferences({
            transfers: {
              email: managerPreferences.transfers?.email ?? true,
              push: managerPreferences.transfers?.push ?? true,
            },
            preMatch: {
              email: managerPreferences.preMatch?.email ?? true,
              push: managerPreferences.preMatch?.push ?? true,
            },
            scoutingResults: {
              email: managerPreferences.scoutingResults?.email ?? true,
              push: managerPreferences.scoutingResults?.push ?? true,
            },
            postMatch: {
              email: managerPreferences.postMatch?.email ?? true,
              push: managerPreferences.postMatch?.push ?? true,
            },
            announcements: {
              email: managerPreferences.announcements?.email ?? true,
              push: managerPreferences.announcements?.push ?? true,
            },
          });
          return; // Exit early if manager preferences were loaded successfully
        }

        // Fall back to local storage if manager preferences are not available
        console.log('No preferences found in manager object, falling back to local storage');

        // Fall back to local storage if API fails
        for (const type of notificationTypes) {
          const emailPref = await AsyncStorage.getItem(`@notification_${type.key}_email`);
          const pushPref = await AsyncStorage.getItem(`@notification_${type.key}_push`);

          if (emailPref !== null || pushPref !== null) {
            setPreferences((prev) => ({
              ...prev,
              [type.key]: {
                email: emailPref === null ? true : emailPref === 'true',
                push: pushPref === null ? true : pushPref === 'true',
              },
            }));
          }
        }
      } catch (error) {
        console.error('Error loading notification preferences:', error);
      }
    };

    loadPreferences();
  }, [manager]);

  // Request push notification permissions
  const requestPushPermissions = async () => {
    // if (Platform.OS === 'web') {
    //   console.log('Push notifications not supported on web');
    //   return true; // Return true on web to continue the flow
    // }

    try {
      const { status: existingStatus } = await Notifications.getPermissionsAsync();
      let finalStatus = existingStatus;

      // Only ask if permissions have not already been determined
      if (existingStatus !== 'granted') {
        const { status } = await Notifications.requestPermissionsAsync();
        finalStatus = status;
      }

      // Return true if permission was granted
      return finalStatus === 'granted';
    } catch (error) {
      console.error('Error requesting notification permissions:', error);
      return false;
    }
  };

  const handleSave = async () => {
    setIsSaving(true);
    try {
      // Check if any push notifications are enabled
      const anyPushEnabled = Object.values(preferences).some((pref) => pref.push);

      // Request push notification permissions if needed
      let pushToken = undefined;
      if (anyPushEnabled) {
        const permissionGranted = await requestPushPermissions();
        if (permissionGranted /*&& Platform.OS !== 'web'*/) {
          try {
            // Get the push notification token
            const tokenData = await Notifications.getExpoPushTokenAsync();
            pushToken = tokenData.data;
            console.log('Push notification token:', pushToken);
          } catch (tokenError) {
            console.error('Error getting push notification token:', tokenError);
          }
        } else if (!permissionGranted /*&& Platform.OS !== 'web'*/) {
          console.log('Push notification permission denied');
          // We still save preferences even if permission denied
        }
      }

      // Create request body for the API
      const requestBody: NotificationPreferencesRequestBody = {
        transfers: {
          email: preferences.transfers.email,
          push: preferences.transfers.push,
        },
        preMatch: {
          email: preferences.preMatch.email,
          push: preferences.preMatch.push,
        },
        scoutingResults: {
          email: preferences.scoutingResults.email,
          push: preferences.scoutingResults.push,
        },
        postMatch: {
          email: preferences.postMatch.email,
          push: preferences.postMatch.push,
        },
        announcements: {
          email: preferences.announcements.email,
          push: preferences.announcements.push,
        },
      };

      // Add push notification token to request body if available
      if (pushToken) {
        requestBody.pushNotificationToken = pushToken;
      }

      // Save notification preferences to the API
      await callApi('/manager/notification-preferences', {
        method: 'PUT',
        body: JSON.stringify(requestBody),
      });

      // Save notification preferences to AsyncStorage for local state
      const savePromises = [];

      // Save each notification type and channel
      for (const type of notificationTypes) {
        savePromises.push(
          AsyncStorage.setItem(
            `@notification_${type.key}_email`,
            preferences[type.key].email.toString()
          )
        );
        savePromises.push(
          AsyncStorage.setItem(
            `@notification_${type.key}_push`,
            preferences[type.key].push.toString()
          )
        );
      }

      // Mark notification settings as seen
      savePromises.push(AsyncStorage.setItem(NOTIFICATION_SETTINGS_SEEN_KEY, 'true'));

      // Wait for all saves to complete
      await Promise.all(savePromises);

      // Update notification scheduling based on new preferences
      await updateNotificationScheduling();

      // Show confirmation
      setShowConfirmation(true);
    } catch (error) {
      console.error('Error saving notification settings:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleContinue = () => {
    if (!manager || !manager.firstName) {
      router.replace('/manager-profile');
    } else {
      router.replace('/');
    }
  };

  // For debugging/testing purposes
  const resetNotificationSettings = async () => {
    try {
      await AsyncStorage.removeItem(NOTIFICATION_SETTINGS_SEEN_KEY);
      alert('Notification settings flag reset. You will see this screen again on next login.');
    } catch (error) {
      console.error('Error resetting notification settings flag:', error);
    }
  };

  return (
    <Container>
      <Title>Notification Settings</Title>
      <Description>
        Welcome to Jumpers for Goalposts! Please configure your notification preferences below. You
        can change these settings later.
      </Description>

      {/* Column headers for toggles */}
      <View style={{ flexDirection: 'row', justifyContent: 'space-between', marginBottom: 15 }}>
        <View style={{ flex: 1 }} />
        <ToggleRow>
          <ToggleColumn>
            <IconContainer>
              <MaterialIcons name="email" size={20} color={theme.colors.text.primary} />
            </IconContainer>
            <ColumnHeaderText>Email</ColumnHeaderText>
          </ToggleColumn>
          <ToggleColumn>
            <IconContainer>
              <MaterialIcons name="smartphone" size={20} color={theme.colors.text.primary} />
            </IconContainer>
            <ColumnHeaderText>Push</ColumnHeaderText>
          </ToggleColumn>
        </ToggleRow>
      </View>

      {/* Notification settings */}
      {notificationTypes.map((type) => (
        <SettingContainer key={type.key}>
          <SettingHeader>
            <IconContainer>
              <FontAwesome name={type.icon} size={20} color={theme.colors.text.primary} />
            </IconContainer>
            <SettingLabel>{type.title}</SettingLabel>
          </SettingHeader>
          <SettingContent>
            <SettingDescription>{type.description}</SettingDescription>
            <ToggleRow>
              <ToggleColumn>
                <Switch
                  value={preferences[type.key].email}
                  onValueChange={(value) =>
                    setPreferences((prev) => ({
                      ...prev,
                      [type.key]: { ...prev[type.key], email: value },
                    }))
                  }
                  trackColor={{ false: '#767577', true: '#81b0ff' }}
                  thumbColor={preferences[type.key].email ? theme.colors.primary : '#f4f3f4'}
                />
              </ToggleColumn>
              <ToggleColumn>
                <Switch
                  value={preferences[type.key].push}
                  onValueChange={(value) =>
                    setPreferences((prev) => ({
                      ...prev,
                      [type.key]: { ...prev[type.key], push: value },
                    }))
                  }
                  trackColor={{ false: '#767577', true: '#81b0ff' }}
                  thumbColor={preferences[type.key].push ? theme.colors.primary : '#f4f3f4'}
                />
              </ToggleColumn>
            </ToggleRow>
          </SettingContent>
        </SettingContainer>
      ))}

      <ButtonContainer>
        <SaveButton onPress={handleSave} disabled={isSaving}>
          {isSaving ? (
            <ActivityIndicator size="small" color="white" />
          ) : (
            <ButtonText>Save Preferences</ButtonText>
          )}
        </SaveButton>
      </ButtonContainer>

      {/* Debug section - only visible in development */}
      {__DEV__ && (
        <DebugContainer>
          <Text style={{ textAlign: 'center', marginBottom: 10 }}>Debug Options</Text>
          <DebugButton onPress={resetNotificationSettings}>
            <ButtonText>Reset Notification Settings Flag</ButtonText>
          </DebugButton>
        </DebugContainer>
      )}

      <CrossPlatformAlert
        visible={showConfirmation}
        title="Settings Saved"
        message="Your notification preferences have been saved."
        buttons={[
          {
            text: 'Continue',
            onPress: handleContinue,
          },
        ]}
        onDismiss={handleContinue}
      />
    </Container>
  );
};

export default NotificationSettingsScreen;
