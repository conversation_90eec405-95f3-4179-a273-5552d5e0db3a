import { MaterialIcons } from '@expo/vector-icons';
import React, { useEffect, useState } from 'react';
import { Image, Modal, TextInput, Dimensions, StatusBar } from 'react-native';
import styled, { DefaultTheme } from 'styled-components/native';
import { callApi } from '../api/client';
import { useManager } from '../context/ManagerContext';
import { TransferListPlayer } from '../hooks/useTransferListPlayers';
import { Team } from '../models/team';
import {
  PlayerLike,
  calculateCurrentEnergy,
  calculateEnergyByNextMatch,
  formatPlayerValue,
} from '../utils/PlayerUtils';
import { CrossPlatformAlert } from './CrossPlatformAlert';
import { Text } from './Text';

interface StyledProps {
  theme: DefaultTheme;
}

interface PlayerDetailViewProps {
  player: PlayerLike;
  team?: Team;
  onClose: () => void;
}

const DetailView = styled.View`
  flex: 1;
  background-color: ${(props: StyledProps) => props.theme.colors.surface};
  padding: 16px;
  width: 100%;
  height: 100%;
`;

const ScrollContainer = styled.ScrollView`
  flex: 1;
`;

const HeaderContainer = styled.View`
  align-items: center;
  margin-bottom: 24px;
`;

const PlayerNameText = styled(Text)`
  font-size: 28px;
  color: ${(props: StyledProps) => props.theme.colors.text.primary};
  font-family: 'NunitoBold';
  text-align: center;
`;

const PlayerIdText = styled(Text)`
  font-size: 14px;
  color: ${(props: StyledProps) => props.theme.colors.text.secondary};
  font-family: 'Nunito';
  margin-top: 4px;
`;

const InfoContainer = styled.View`
  flex-direction: row;
  margin-bottom: 24px;
`;

const PlayerImageContainer = styled.View`
  width: 120px;
  height: 120px;
  margin-right: 16px;
  border-radius: 8px;
  overflow: hidden;
  background-color: #f0f0f0;
`;

const PlayerStatsContainer = styled.View`
  flex: 1;
  justify-content: center;
`;

const StatusContainer = styled.View`
  background-color: #e3172a;
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 24px;
  align-items: center;
`;

const StatusText = styled(Text)`
  font-size: 16px;
  color: white;
  font-family: 'NunitoBold';
`;

const AttributeTableContainer = styled.View`
  margin-bottom: 24px;
`;

const AttributeTableHeader = styled.View`
  background-color: ${(props: StyledProps) => props.theme.colors.primary};
  padding: 8px 12px;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
`;

const AttributeTableHeaderText = styled(Text)`
  font-size: 16px;
  color: white;
  font-family: 'NunitoBold';
`;

const AttributeRow = styled.View`
  flex-direction: row;
  padding: 8px 12px;
  border-bottom-width: 1px;
  border-bottom-color: #e0e0e0;
  background-color: ${(props: StyledProps) => props.theme.colors.background};
`;

const AttributeName = styled(Text)`
  flex: 1;
  font-size: 16px;
  color: ${(props: StyledProps) => props.theme.colors.text.primary};
  font-family: 'Nunito';
`;

const AttributeValue = styled(Text)`
  font-size: 16px;
  color: ${(props: StyledProps) => props.theme.colors.text.primary};
  font-family: 'NunitoBold';
`;

const CloseButton = styled.TouchableOpacity`
  position: absolute;
  top: 16px;
  left: 16px;
  z-index: 1;
  background-color: ${(props: StyledProps) => props.theme.colors.primary};
  width: 40px;
  height: 40px;
  border-radius: 20px;
  justify-content: center;
  align-items: center;
`;

const DetailText = styled(Text)`
  font-size: 16px;
  color: ${(props: StyledProps) => props.theme.colors.text.primary};
  font-family: 'NunitoBold';
  margin-bottom: 8px;
`;

const TransferButton = styled.TouchableOpacity`
  background-color: ${(props: StyledProps) => props.theme.colors.primary};
  padding: 12px;
  border-radius: 8px;
  align-items: center;
  margin-top: 16px;
  margin-bottom: 24px;
`;

const ButtonText = styled(Text)`
  color: white;
  font-family: 'NunitoBold';
  font-size: 16px;
`;

const ModalContainer = styled.View`
  flex: 1;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.5);
  padding: 20px;
`;

const ModalContent = styled.View`
  background-color: ${(props: StyledProps) => props.theme.colors.background};
  border-radius: 12px;
  padding: 20px;
  width: 90%;
  max-width: 400px;
`;

const ModalTitle = styled(Text)`
  font-family: 'NunitoBold';
  font-size: 20px;
  margin-bottom: 16px;
  text-align: center;
`;

const InputContainer = styled.View`
  margin-bottom: 16px;
`;

const Label = styled(Text)`
  font-family: 'Nunito';
  font-size: 16px;
  margin-bottom: 8px;
`;

const Input = styled(TextInput)`
  background-color: ${(props: StyledProps) => props.theme.colors.surface};
  padding: 12px;
  border-radius: 8px;
  font-family: 'Nunito';
  font-size: 16px;
  color: ${(props: StyledProps) => props.theme.colors.text.primary};
`;

const ButtonContainer = styled.View`
  flex-direction: row;
  justify-content: space-between;
  margin-top: 16px;
  gap: 12px;
`;

const BidHistoryContainer = styled.View`
  margin-bottom: 24px;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #e0e0e0;
`;

const BidHistoryHeader = styled.View`
  background-color: ${(props: StyledProps) => props.theme.colors.primary};
  padding: 8px 12px;
`;

const BidHistoryHeaderText = styled(Text)`
  font-size: 16px;
  color: white;
  font-family: 'NunitoBold';
`;

const BidHistoryRow = styled.View`
  flex-direction: row;
  padding: 12px;
  border-bottom-width: 1px;
  border-bottom-color: #e0e0e0;
  background-color: ${(props: StyledProps) => props.theme.colors.background};
`;

const BidHistoryTeamName = styled(Text)`
  flex: 2;
  font-size: 14px;
  color: ${(props: StyledProps) => props.theme.colors.text.primary};
  font-family: 'Nunito';
`;

const BidHistoryAmount = styled(Text)`
  flex: 1;
  font-size: 14px;
  text-align: right;
  color: ${(props: StyledProps) => props.theme.colors.text.primary};
  font-family: 'NunitoBold';
`;

const PlayerDetailView: React.FC<PlayerDetailViewProps> = ({ player, team, onClose }) => {
  const { manager, team: userTeam } = useManager();
  const [isOfferModalVisible, setIsOfferModalVisible] = useState(false);
  const [offerAmount, setOfferAmount] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showAlert, setShowAlert] = useState(false);
  const [alertMessage, setAlertMessage] = useState({ title: '', message: '' });
  const [timeRemaining, setTimeRemaining] = useState('');
  const [maxBid, setMaxBid] = useState<number | null>(null);
  const [isHighestBidder, setIsHighestBidder] = useState<boolean>(false);
  
  // Get screen dimensions
  const screenWidth = Dimensions.get('window').width;
  const screenHeight = Dimensions.get('window').height;

  //const positions = calculatePlayerPosition(player);
  const formattedValue = formatPlayerValue(player.value);
  const now = Date.now();
  const isInjured = player.injuredUntil && player.injuredUntil > now;
  const isSuspended = player.suspendedForGames > 0;

  // Check if player belongs to user's team
  const isPlayerInUserTeam = userTeam && player.teamId === userTeam.teamId;

  // Check if player is on auction (no team)
  const isAuctionPlayer = player.teamId === '';

  // Get auction details if this is a TransferListPlayer
  const transferListPlayer = isAuctionPlayer ? (player as TransferListPlayer) : null;
  const currentBid = transferListPlayer?.bidHistory?.length
    ? Math.max(...(transferListPlayer.bidHistory || []).map((bid) => bid.maximumBid))
    : transferListPlayer?.auctionStartPrice || 0;
  const formattedCurrentBid = currentBid ? formatPlayerValue(currentBid) : 'No bids';

  /*  // Fetch current bid status when component loads
  useEffect(() => {
    if (!isAuctionPlayer || !player.playerId || !manager?.team) return;

    const fetchBidStatus = async () => {
      try {
        const response = await callApi(`/transfer/bid?player=${player.playerId}&myTeam=${manager.team}`, {
          method: 'GET',
        });

        const bidStatus = await response.json();
        if (bidStatus.maxBid) {
          setMaxBid(bidStatus.maxBid);
          setIsHighestBidder(bidStatus.highestBidder);
        }
      } catch (error) {
        console.error('Error fetching bid status:', error);
      }
    };

    fetchBidStatus();
  }, [isAuctionPlayer, player.playerId, manager?.team]);*/

  // Update time remaining for auction
  useEffect(() => {
    if (!isAuctionPlayer || !transferListPlayer?.auctionEndTime) return;

    const updateTimeRemaining = () => {
      const currentTime = Date.now();
      const endTime = transferListPlayer.auctionEndTime;
      const timeLeft = endTime - currentTime;

      if (timeLeft <= 0) {
        setTimeRemaining('Auction ended');
        return;
      }

      // Format time remaining
      const days = Math.floor(timeLeft / (1000 * 60 * 60 * 24));
      const hours = Math.floor((timeLeft % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
      const minutes = Math.floor((timeLeft % (1000 * 60 * 60)) / (1000 * 60));
      const seconds = Math.floor((timeLeft % (1000 * 60)) / 1000);

      if (days > 0) {
        setTimeRemaining(`${days}d ${hours}h remaining`);
      } else if (hours > 0) {
        setTimeRemaining(`${hours}h ${minutes}m remaining`);
      } else if (minutes > 0) {
        setTimeRemaining(`${minutes}m ${seconds}s remaining`);
      } else {
        setTimeRemaining(`${seconds}s remaining`);
      }
    };

    // Update immediately
    updateTimeRemaining();

    // Then update every second
    const interval = setInterval(updateTimeRemaining, 1000);
    return () => clearInterval(interval);
  }, [isAuctionPlayer, transferListPlayer?.auctionEndTime]);

  // Format the injury date
  const formatInjuryDate = (timestamp: number) => {
    const date = new Date(timestamp);
    return date.toLocaleDateString();
  };

  // Handle transfer offer submission
  const handleSubmitOffer = async () => {
    if (!offerAmount || isNaN(Number(offerAmount))) {
      setAlertMessage({
        title: 'Invalid Amount',
        message: 'Please enter a valid transfer amount.',
      });
      setShowAlert(true);
      return;
    }

    // For auction players, validate bid amount
    if (isAuctionPlayer && transferListPlayer) {
      const bidAmount = Number(offerAmount);
      const minBidRequired = currentBid + 1000;

      if (bidAmount < minBidRequired) {
        setAlertMessage({
          title: 'Bid Too Low',
          message: `Your bid must be at least ${formatPlayerValue(minBidRequired)} (current bid + £1K).`,
        });
        setShowAlert(true);
        return;
      }

      // Check if auction has ended
      if (transferListPlayer.auctionEndTime < Date.now()) {
        setAlertMessage({
          title: 'Auction Ended',
          message: 'This auction has already ended.',
        });
        setShowAlert(true);
        return;
      }
    }

    try {
      setIsSubmitting(true);

      if (isAuctionPlayer) {
        // Submit bid for auction player
        const bidResponse = await callApi(`/transfer/bid`, {
          method: 'POST',
          body: JSON.stringify({
            player: player.playerId,
            maxBid: Number(offerAmount),
            myTeam: manager?.team,
          }),
        });

        // Process the response to get maxBid and highestBidder status
        setMaxBid(bidResponse.maxBid);
        setIsHighestBidder(bidResponse.highestBidder);

        setIsOfferModalVisible(false);
        setAlertMessage({
          title: 'Success',
          message: `Bid submitted successfully! ${bidResponse.highestBidder ? 'You are the highest bidder!' : 'You are not the highest bidder.'}`,
        });
      } else {
        // Submit transfer offer for team player
        await callApi(`/transfer/offer`, {
          method: 'POST',
          body: JSON.stringify({
            player: player.playerId,
            offer: Number(offerAmount),
            theirTeam: player.teamId,
            myTeam: manager?.team,
          }),
        });

        setIsOfferModalVisible(false);
        setAlertMessage({
          title: 'Success',
          message: 'Transfer offer submitted successfully!',
        });
      }

      setShowAlert(true);
    } catch (error) {
      console.error('Error submitting transfer offer/bid:', error);
      setAlertMessage({
        title: 'Error',
        message: `Failed to submit ${isAuctionPlayer ? 'bid' : 'transfer offer'}. Please try again.`,
      });
      setShowAlert(true);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Group attributes by category
  const goalkeepingAttributes = [
    { name: 'Reflexes', value: player.attributes.reflexes },
    { name: 'Positioning', value: player.attributes.positioning },
    { name: 'Shot Stopping', value: player.attributes.shotStopping },
  ];

  const defendingAttributes = [
    { name: 'Tackling', value: player.attributes.tackling },
    { name: 'Marking', value: player.attributes.marking },
    { name: 'Heading', value: player.attributes.heading },
  ];

  const midfieldingAttributes = [
    { name: 'Passing', value: player.attributes.passing },
    { name: 'Vision', value: player.attributes.vision },
    { name: 'Ball Control', value: player.attributes.ballControl },
  ];

  const attackingAttributes = [
    { name: 'Finishing', value: player.attributes.finishing },
    { name: 'Pace', value: player.attributes.pace },
    { name: 'Crossing', value: player.attributes.crossing },
  ];

  return (
    <Modal
      visible={true}
      transparent={false}
      animationType="slide"
      onRequestClose={onClose}
    >
      <StatusBar barStyle="dark-content" />
      <DetailView>
        <CloseButton onPress={onClose}>
          <MaterialIcons name="arrow-back" size={24} color="white" />
        </CloseButton>

        <ScrollContainer showsVerticalScrollIndicator={false}>
          {/* Player Name and ID */}
          <HeaderContainer>
            <PlayerNameText>{`${player.firstName} ${player.surname}`}</PlayerNameText>
            <PlayerIdText>{`ID: ${player.playerId}`}</PlayerIdText>
          </HeaderContainer>

          {/* Player Image and Stats */}
          <InfoContainer>
            <PlayerImageContainer>
              <Image
                source={require('../../assets/mugshot.png')}
                style={{ width: '100%', height: '100%' }}
                resizeMode="cover"
              />
            </PlayerImageContainer>
            <PlayerStatsContainer>
              <DetailText>{`Age: ${player.age}`}</DetailText>
              <DetailText>{`Value: ${formattedValue}`}</DetailText>
              <DetailText>{`Energy: ${calculateCurrentEnergy(player.attributes.stamina, player.energy, player.lastMatchPlayed)}%`}</DetailText>
              {team && (
                <DetailText>{`Energy by Next Match: ${calculateEnergyByNextMatch(player.attributes.stamina, player.energy, player.lastMatchPlayed, team.nextFixture.date)}%`}</DetailText>
              )}
            </PlayerStatsContainer>
          </InfoContainer>

          {/* Transfer Offer Button - only show if player is not in user's team */}
          {!isPlayerInUserTeam && (
            <>
              {isAuctionPlayer && transferListPlayer && (
                <StatusContainer style={{ backgroundColor: '#2E7D32' }}>
                  <StatusText>
                    Current Bid: {maxBid ? formatPlayerValue(maxBid) : formattedCurrentBid}
                  </StatusText>
                  <StatusText>{timeRemaining}</StatusText>
                  {maxBid && (
                    <StatusText
                      style={{
                        color: isHighestBidder ? '#FFEB3B' : '#FFFFFF',
                        fontFamily: 'NunitoBold',
                      }}
                    >
                      {isHighestBidder
                        ? '✓ You are the highest bidder!'
                        : '✗ You are not the highest bidder'}
                    </StatusText>
                  )}
                </StatusContainer>
              )}
              <TransferButton
                onPress={() => {
                  // Prefill the offer amount
                  if (isAuctionPlayer && currentBid) {
                    // For auctions, prefill with minimum bid (current bid + 1000)
                    setOfferAmount((currentBid + 1000).toString());
                  } else {
                    // For transfers, prefill with player value
                    setOfferAmount(player.value.toFixed(0).toString());
                  }
                  setIsOfferModalVisible(true);
                }}
              >
                <ButtonText>{player.teamId === '' ? 'Bid Now' : 'Submit Transfer Offer'}</ButtonText>
              </TransferButton>

              {/* Bid History Section */}
              {isAuctionPlayer && transferListPlayer && (
                <BidHistoryContainer>
                  <BidHistoryHeader>
                    <BidHistoryHeaderText>Bid History</BidHistoryHeaderText>
                  </BidHistoryHeader>

                  {/* Initial Price Row */}
                  <BidHistoryRow>
                    <BidHistoryTeamName>Initial Price</BidHistoryTeamName>
                    <BidHistoryAmount>{formatPlayerValue(transferListPlayer.auctionStartPrice)}</BidHistoryAmount>
                  </BidHistoryRow>

                  {/* Bid History Rows */}
                  {transferListPlayer.bidHistory && transferListPlayer.bidHistory.length > 0 ? (
                    // Sort bids by bidTime (oldest first)
                    [...transferListPlayer.bidHistory]
                      .sort((a, b) => a.bidTime - b.bidTime)
                      .map((bid, index) => {
                        // If maximum bid is higher than current price, show current price instead
                        const displayBidAmount = 
                          bid.maximumBid > transferListPlayer.auctionCurrentPrice 
                            ? transferListPlayer.auctionCurrentPrice 
                            : bid.maximumBid;

                        return (
                          <BidHistoryRow key={`bid-${index}`}>
                            <BidHistoryTeamName>{bid.teamName}</BidHistoryTeamName>
                            <BidHistoryAmount>{formatPlayerValue(displayBidAmount)}</BidHistoryAmount>
                          </BidHistoryRow>
                        );
                      })
                  ) : (
                    <BidHistoryRow>
                      <BidHistoryTeamName>No bids yet</BidHistoryTeamName>
                      <BidHistoryAmount></BidHistoryAmount>
                    </BidHistoryRow>
                  )}
                </BidHistoryContainer>
              )}
            </>
          )}

          {/* Status Messages */}
          {isInjured && (
            <StatusContainer>
              <StatusText>{`Injured until ${formatInjuryDate(player.injuredUntil!)}`}</StatusText>
            </StatusContainer>
          )}

          {isSuspended && (
            <StatusContainer>
              <StatusText>{`Suspended for ${player.suspendedForGames} games`}</StatusText>
            </StatusContainer>
          )}

          {/* Goalkeeping Attributes */}
          <AttributeTableContainer>
            <AttributeTableHeader>
              <AttributeTableHeaderText>Goalkeeping</AttributeTableHeaderText>
            </AttributeTableHeader>
            {goalkeepingAttributes.map((attr, index) => (
              <AttributeRow key={`gk-${index}`}>
                <AttributeName>{attr.name}</AttributeName>
                <AttributeValue>{attr.value}</AttributeValue>
              </AttributeRow>
            ))}
          </AttributeTableContainer>

          {/* Defending Attributes */}
          <AttributeTableContainer>
            <AttributeTableHeader>
              <AttributeTableHeaderText>Defending</AttributeTableHeaderText>
            </AttributeTableHeader>
            {defendingAttributes.map((attr, index) => (
              <AttributeRow key={`df-${index}`}>
                <AttributeName>{attr.name}</AttributeName>
                <AttributeValue>{attr.value}</AttributeValue>
              </AttributeRow>
            ))}
          </AttributeTableContainer>

          {/* Midfielding Attributes */}
          <AttributeTableContainer>
            <AttributeTableHeader>
              <AttributeTableHeaderText>Midfielding</AttributeTableHeaderText>
            </AttributeTableHeader>
            {midfieldingAttributes.map((attr, index) => (
              <AttributeRow key={`mf-${index}`}>
                <AttributeName>{attr.name}</AttributeName>
                <AttributeValue>{attr.value}</AttributeValue>
              </AttributeRow>
            ))}
          </AttributeTableContainer>

          {/* Attacking Attributes */}
          <AttributeTableContainer>
            <AttributeTableHeader>
              <AttributeTableHeaderText>Attacking</AttributeTableHeaderText>
            </AttributeTableHeader>
            {attackingAttributes.map((attr, index) => (
              <AttributeRow key={`at-${index}`}>
                <AttributeName>{attr.name}</AttributeName>
                <AttributeValue>{attr.value}</AttributeValue>
              </AttributeRow>
            ))}
          </AttributeTableContainer>
        </ScrollContainer>

        {/* Transfer Offer Modal */}
        <Modal
          visible={isOfferModalVisible}
          transparent
          animationType="fade"
          onRequestClose={() => setIsOfferModalVisible(false)}
        >
          <ModalContainer>
            <ModalContent>
              <ModalTitle>{player.teamId === '' ? 'Submit Bid' : 'Submit Transfer Offer'}</ModalTitle>

              <InputContainer>
                <Label>
                  Player: {player.firstName} {player.surname}
                </Label>

                {isAuctionPlayer && transferListPlayer ? (
                  <>
                    <Label>Player Value: {formattedValue}</Label>
                    <Label>
                      Current Bid: {maxBid ? formatPlayerValue(maxBid) : formattedCurrentBid}
                    </Label>
                    {maxBid && (
                      <Label
                        style={{
                          color: isHighestBidder ? '#2E7D32' : '#e3172a',
                          fontFamily: 'NunitoBold',
                        }}
                      >
                        {isHighestBidder
                          ? '✓ You are the highest bidder!'
                          : '✗ You are not the highest bidder'}
                      </Label>
                    )}
                    <Label>Time Remaining: {timeRemaining}</Label>
                    <Label>Minimum Bid: {formatPlayerValue((maxBid || currentBid) + 1000)}</Label>
                    <Label>Your Bid:</Label>
                  </>
                ) : (
                  <>
                    <Label>Current Value: {formattedValue}</Label>
                    <Label>Offer Amount:</Label>
                  </>
                )}

                <Input
                  value={offerAmount}
                  onChangeText={setOfferAmount}
                  placeholder="Enter amount"
                  keyboardType="numeric"
                />
              </InputContainer>

              <ButtonContainer>
                <TransferButton
                  onPress={() => setIsOfferModalVisible(false)}
                  style={{ backgroundColor: '#888' }}
                >
                  <ButtonText>Cancel</ButtonText>
                </TransferButton>
                <TransferButton onPress={handleSubmitOffer} disabled={isSubmitting}>
                  <ButtonText>
                    {isSubmitting ? 'Submitting...' : isAuctionPlayer ? 'Submit Bid' : 'Submit Offer'}
                  </ButtonText>
                </TransferButton>
              </ButtonContainer>
            </ModalContent>
          </ModalContainer>
        </Modal>

        {/* Alert for success/error messages */}
        <CrossPlatformAlert
          visible={showAlert}
          title={alertMessage.title}
          message={alertMessage.message}
          buttons={[
            {
              text: 'OK',
              onPress: () => setShowAlert(false),
            },
          ]}
          onDismiss={() => setShowAlert(false)}
        />
      </DetailView>
    </Modal>
  );
};

export default PlayerDetailView;